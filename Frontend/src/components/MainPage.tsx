import React, { useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import useFetchCourseAnalysis from '../hooks/useFetchCourseAnalysis';
import { useNavigate } from 'react-router-dom';


const MainPage = () => {
  const navigate = useNavigate();

  const goToLogin = () => {
    navigate("/login", {
      state: {
        fromQR: false,
        programId: 0 , // carry the courseId here
      },
    });
  };

  const goToRegister = () => {
    navigate("/register");
  };

  const { courseAnalysis } = useFetchCourseAnalysis("TOP5");

  useEffect(() => {
   localStorage.clear();
  }, []);

  // Fallback data in case API fails
  const fallbackCourses = [
    {
      id: 1,
      course: "Yoga & Meditation",
      course_imagepath: "yoga.jpg",
      category: "Physical Wellness"
    },
    {
      id: 2,
      course: "Nutrition Planning",
      course_imagepath: "nutrition.jpg",
      category: "Health & Diet"
    },
    {
      id: 3,
      course: "Mental Wellness",
      course_imagepath: "mental.jpg",
      category: "Mental Health"
    }
  ];

  const displayCourses = courseAnalysis && courseAnalysis.length > 0 ? courseAnalysis : fallbackCourses;


  // const cardItems = [
  //   {
  //     title: 'Yoga & Meditation',
  //     image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=500&h=500&fit=crop'
  //   },
  //   {
  //     title: 'Nutrition Planning',
  //     image: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=500&h=500&fit=crop'
  //   },
  //   {
  //     title: 'Mental Wellness',
  //     image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=500&h=500&fit=crop'
  //   },
  //   {
  //     title: 'Physical Fitness',
  //     image: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=500&h=500&fit=crop'
  //   }
  // ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50 flex flex-col justify-center py-4 sm:py-6 md:py-8">
      <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6 sm:space-y-8 md:space-y-10">
        {/* Logo Box */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 lg:p-10 shadow-primary-lg">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-3 mb-4 sm:mb-6">
            <span
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-secondary-800 animate-float"
              style={{ display: 'inline-block' }}
            >
              w
            </span>
            <span className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold bg-gradient-to-r from-secondary-600 to-secondary-800 bg-clip-text text-transparent">
              Holistic
            </span>
            <span className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold bg-gradient-to-r from-accent-600 to-accent-800 bg-clip-text text-transparent">
              Wellness
            </span>
          </div>
          <div>
            <p className="italic text-neutral-700 text-center font-sans text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto px-2">
              To achieve health, harmony and quality in every aspect of life-Body, Mind, Soul & Family.
            </p>
          </div>
        </div>

        {/* <div ref={loginRef}>
          <LoginBox />
        </div>

        <FeaturesSection onRegisterClick={scrollToLogin} /> */}

        {/* Programs Carousel */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 lg:p-10 shadow-primary-lg">
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-secondary-800 mb-4 sm:mb-6 md:mb-8 text-center">Top Choices</h2>

          <Swiper
            modules={[Autoplay]}
            spaceBetween={12}
            slidesPerView={1.2}
            centeredSlides={true}
            loop={displayCourses.length > 1}
            autoplay={{
              delay: 2000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              480: {
                slidesPerView: 1.5,
                spaceBetween: 15,
              },
              640: {
                slidesPerView: 2,
                spaceBetween: 20,
                centeredSlides: false,
              },
              768: {
                slidesPerView: 2.5,
                spaceBetween: 20,
                centeredSlides: false,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 24,
                centeredSlides: false,
              },
            }}
            className="w-full"
          >
            {displayCourses.map((item) => (
              <SwiperSlide key={item.id}>
                <div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 h-full">
                  <div className="aspect-square">
                    <img
                       src={`/assets/${item.course_imagepath}`}
                      alt={item.category || item.course}
                      className="w-full h-full object-cover"
                      height="100"
                      width="100"
                      onError={(e) => {
                        e.currentTarget.src = 'https://via.placeholder.com/300x300?text=Wellness+Program';
                      }}
                    />
                  </div>
                  <div className="p-2 sm:p-3">
                    <h3 className="text-sm sm:text-base font-semibold text-gray-800 line-clamp-2">
                      {item.course}
                    </h3>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* <div className="flex max-w-md pt-3 pb-3 space-x-4">
         
            <button className="flexgrow p-3 text-white bg-blue-600 rounded-lg shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300"
             onClick={()=> {navigate('/register'); }}// Navigate to the registration page}
            >
                  New User
            </button>
        
            <button className="flex-grow p-3 text-white bg-blue-600 rounded-lg shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300"
            onClick={()=> {navigate('/login'); }}// Navigate to the login page}
            >
                  Existing User
            </button>
        </div> */}
        <div className="flex flex-col sm:flex-row mx-auto pt-4 sm:pt-6 pb-4 sm:pb-6 space-y-3 sm:space-y-0 sm:space-x-4 md:space-x-6 max-w-sm sm:max-w-md px-4 sm:px-0">
          <button
            className="flex-grow p-3 sm:p-4 md:p-5 text-white bg-secondary-600 rounded-xl shadow-secondary hover:bg-secondary-700 hover:shadow-secondary transition-all duration-300 font-medium text-sm sm:text-base md:text-lg"
            onClick={goToRegister}
          >
            New User
          </button>
          <button
            className="flex-grow p-3 sm:p-4 md:p-5 text-white bg-accent-600 rounded-xl shadow-accent hover:bg-accent-700 hover:shadow-accent transition-all duration-300 font-medium text-sm sm:text-base md:text-lg"
            onClick={goToLogin}
          >
            Existing User
          </button>
        </div>


        {/* <div ref={loginRef}>
          <LoginBox />
        </div> */}

        {/* <FeaturesSection onRegisterClick={scrollToLogin} /> */}

        <div className="flex flex-col justify-center items-center mt-6 sm:mt-8 px-4">
           <p className="text-neutral-600 text-sm sm:text-base mb-3 sm:mb-4">Brought to you by</p>
           <div className="text-center font-serif">
              <a href="https://www.bhaktivedantahospital.com/" className="text-decoration-none hover:opacity-80 transition-opacity">
                 <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-secondary-700">BHAKTIVEDANTA</h2>
                 <p className="text-base sm:text-lg md:text-xl text-secondary-600">HOSPITAL & RESEARCH INSTITUTE</p>
              </a>
           </div>
        </div>
      </div>
    </div>
  );
};

export default MainPage;